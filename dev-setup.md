# Pedma AI - Local Development Setup Guide

## Prerequisites
- Node.js 20+ installed
- Firebase CLI installed (✅ Done)
- Your API keys ready

## Step 1: Configure API Keys

### 1.1 Update Main .env File
Copy your API keys to the main `.env` file:
```bash
# Firebase Configuration (get from Firebase Console)
FIREBASE_API_KEY=your_actual_firebase_api_key
FIREBASE_AUTH_DOMAIN=pedma-ai.firebaseapp.com
FIREBASE_PROJECT_ID=pedma-ai
FIREBASE_STORAGE_BUCKET=pedma-ai.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_actual_sender_id
FIREBASE_APP_ID=your_actual_app_id

# AI Service API Keys
GROQ_API_KEY=your_groq_api_key
DEEPGRAM_API_KEY=your_deepgram_api_key
SERPAPI_KEY=your_serpapi_key
ANTHROPIC_API_KEY=your_anthropic_api_key
```

### 1.2 Update Functions .env File
Copy the same API keys to `functions/.env`:
```bash
GROQ_API_KEY=your_groq_api_key
DEEPGRAM_API_KEY=your_deepgram_api_key
SERPAPI_KEY=your_serpapi_key
ANTHROPIC_API_KEY=your_anthropic_api_key
```

### 1.3 Update Angular Environment
Update `src/environments/environment.ts` with your Firebase config:
```typescript
export const environment = {
  production: false,
  firebase: {
    apiKey: 'your_actual_firebase_api_key',
    authDomain: 'pedma-ai.firebaseapp.com',
    projectId: 'pedma-ai',
    storageBucket: 'pedma-ai.appspot.com',
    messagingSenderId: 'your_actual_sender_id',
    appId: 'your_actual_app_id'
  },
  // ... rest of config
};
```

## Step 2: Firebase Login and Project Setup

### 2.1 Login to Firebase
```bash
firebase login
```

### 2.2 Initialize Firebase Project
```bash
firebase use pedma-ai
```

### 2.3 Deploy Firestore Rules (Optional for local testing)
```bash
firebase deploy --only firestore:rules
firebase deploy --only storage
```

## Step 3: Start Local Development Environment

### 3.1 Start Firebase Emulators
Open a new terminal and run:
```bash
firebase emulators:start
```

This will start:
- Authentication Emulator (port 9099)
- Functions Emulator (port 5001)
- Firestore Emulator (port 8080)
- Storage Emulator (port 9199)
- Emulator UI (port 4000)

### 3.2 Start Angular Development Server
In another terminal:
```bash
ng serve --port 4200
```

## Step 4: Testing the Application

### 4.1 Access the Application
- **Main App**: http://localhost:4200
- **Firebase Emulator UI**: http://localhost:4000
- **Functions**: http://localhost:5001

### 4.2 Test Workflow
1. Open http://localhost:4200
2. Sign in with Google (will use Firebase Auth Emulator)
3. Upload a small audio file (MP3, WAV, M4A)
4. Watch the AI agents process the file
5. Chat with the AI to refine the description

### 4.3 Monitor Progress
- Check the chat panel for real-time agent updates
- Monitor the Firebase Emulator UI for data
- Check browser console for any errors

## Step 5: Troubleshooting

### Common Issues:

**1. Firebase Connection Errors**
- Ensure your Firebase config is correct
- Check that emulators are running
- Verify project ID matches

**2. API Key Errors**
- Double-check all API keys are set
- Ensure no extra spaces or quotes
- Verify keys are valid and have proper permissions

**3. CORS Errors**
- Ensure Firebase Functions are running
- Check that CORS is properly configured

**4. Build Errors**
- Run `npm install` in both root and functions directories
- Ensure TypeScript compilation succeeds: `cd functions && npx tsc`

### Debug Commands:
```bash
# Check Firebase project
firebase projects:list

# Check emulator status
firebase emulators:start --inspect-functions

# Build functions manually
cd functions && npm run build

# Check Angular build
ng build

# View Firebase logs
firebase functions:log
```

## Step 6: Production Deployment (Later)

When ready for production:

### 6.1 Deploy Functions
```bash
cd functions
npm run build
firebase deploy --only functions
```

### 6.2 Deploy Hosting (Vercel)
```bash
ng build --configuration=production
# Then deploy dist/pedma-app to Vercel
```

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Angular App   │    │ Firebase Functions│    │   AI Services   │
│   (Port 4200)   │◄──►│   (Port 5001)    │◄──►│  Groq, Deepgram │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│ Firebase Auth   │    │   Firestore DB   │
│   (Port 9099)   │    │   (Port 8080)    │
└─────────────────┘    └──────────────────┘
```

## Next Steps After Setup

1. **Test with Real Audio**: Upload a short podcast clip
2. **Monitor Agent Performance**: Watch the multi-agent workflow
3. **Refine Prompts**: Adjust agent prompts based on results
4. **Add Error Handling**: Improve error messages and recovery
5. **Performance Optimization**: Optimize for larger files

## Support

If you encounter issues:
1. Check the browser console for errors
2. Monitor the Firebase Emulator UI
3. Check the terminal outputs for both Angular and Firebase
4. Verify all API keys are correctly set
