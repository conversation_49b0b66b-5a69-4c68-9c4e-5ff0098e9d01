import { Injectable, inject } from '@angular/core';
import { Functions, httpsCallable } from '@angular/fire/functions';
import { Observable, from, BehaviorSubject, interval } from 'rxjs';
import { switchMap, takeWhile, map } from 'rxjs/operators';
import { AgentStatus, ProcessingSession, ChatMessage } from '../models/podcast-description.model';

export interface ProcessingRequest {
  sessionId: string;
  audioFileUrl: string;
  userId: string;
  autoMode?: boolean;
}

export interface ChatRequest {
  sessionId: string;
  message: string;
  userId: string;
}

export interface SessionStatusResponse {
  session: ProcessingSession;
  agentContext: any;
  agentStatuses: Record<string, AgentStatus>;
}

@Injectable({
  providedIn: 'root'
})
export class AgentService {
  private functions = inject(Functions);
  
  // Observables for real-time updates
  private agentStatusSubject = new BehaviorSubject<Record<string, AgentStatus>>({});
  public agentStatus$ = this.agentStatusSubject.asObservable();

  private processingStatusSubject = new BehaviorSubject<string>('idle');
  public processingStatus$ = this.processingStatusSubject.asObservable();

  constructor() {}

  /**
   * Start processing an audio file
   */
  startProcessing(request: ProcessingRequest): Observable<any> {
    const processAudio = httpsCallable(this.functions, 'processAudio');
    
    return from(processAudio(request)).pipe(
      map((result: any) => {
        if (result.data.success) {
          this.processingStatusSubject.next('processing');
          // Start polling for status updates
          this.startStatusPolling(request.sessionId);
        }
        return result.data;
      })
    );
  }

  /**
   * Send a chat message to the AI agent
   */
  sendChatMessage(request: ChatRequest): Observable<any> {
    const chatWithAgent = httpsCallable(this.functions, 'chatWithAgent');
    
    return from(chatWithAgent(request)).pipe(
      map((result: any) => result.data)
    );
  }

  /**
   * Get current session status
   */
  getSessionStatus(sessionId: string): Observable<SessionStatusResponse> {
    const getSessionStatus = httpsCallable(this.functions, 'getSessionStatus');
    
    return from(getSessionStatus({ sessionId })).pipe(
      map((result: any) => result.data)
    );
  }

  /**
   * Continue auto mode processing
   */
  continueAutoMode(sessionId: string): Observable<any> {
    const continueAutoMode = httpsCallable(this.functions, 'continueAutoMode');
    
    return from(continueAutoMode({ sessionId })).pipe(
      map((result: any) => result.data)
    );
  }

  /**
   * Start polling for status updates
   */
  private startStatusPolling(sessionId: string): void {
    interval(3000) // Poll every 3 seconds
      .pipe(
        switchMap(() => this.getSessionStatus(sessionId)),
        takeWhile((status) => {
          // Continue polling while processing
          const isProcessing = Object.values(status.agentStatuses || {})
            .some(agent => agent.status === 'processing');
          
          if (!isProcessing) {
            this.processingStatusSubject.next('complete');
          }
          
          return isProcessing;
        })
      )
      .subscribe({
        next: (status) => {
          this.agentStatusSubject.next(status.agentStatuses || {});
        },
        error: (error) => {
          console.error('Status polling error:', error);
          this.processingStatusSubject.next('error');
        }
      });
  }

  /**
   * Get current agent statuses
   */
  getCurrentAgentStatuses(): Record<string, AgentStatus> {
    return this.agentStatusSubject.value;
  }

  /**
   * Get processing status
   */
  getCurrentProcessingStatus(): string {
    return this.processingStatusSubject.value;
  }

  /**
   * Reset service state
   */
  reset(): void {
    this.agentStatusSubject.next({});
    this.processingStatusSubject.next('idle');
  }

  /**
   * Get agent progress summary
   */
  getProgressSummary(): Observable<any> {
    return this.agentStatus$.pipe(
      map(statuses => {
        const agents = Object.values(statuses);
        const totalAgents = agents.length;
        const completedAgents = agents.filter(agent => agent.status === 'complete').length;
        const processingAgents = agents.filter(agent => agent.status === 'processing');
        const errorAgents = agents.filter(agent => agent.status === 'error');

        const overallProgress = totalAgents > 0 ? (completedAgents / totalAgents) * 100 : 0;

        return {
          overallProgress,
          totalAgents,
          completedAgents,
          processingAgents: processingAgents.map(agent => ({
            name: agent.name,
            progress: agent.progress,
            message: agent.message
          })),
          errorAgents: errorAgents.map(agent => ({
            name: agent.name,
            message: agent.message
          })),
          isComplete: completedAgents === totalAgents && totalAgents > 0,
          hasErrors: errorAgents.length > 0
        };
      })
    );
  }

  /**
   * Retry failed agent
   */
  retryAgent(sessionId: string, agentName: string): Observable<any> {
    // This would trigger a specific agent retry
    // For now, we'll restart the entire process
    return this.continueAutoMode(sessionId);
  }

  /**
   * Check if processing is complete
   */
  isProcessingComplete(): Observable<boolean> {
    return this.agentStatus$.pipe(
      map(statuses => {
        const agents = Object.values(statuses);
        if (agents.length === 0) return false;
        
        return agents.every(agent => 
          agent.status === 'complete' || agent.status === 'error'
        );
      })
    );
  }

  /**
   * Get estimated time remaining
   */
  getEstimatedTimeRemaining(): Observable<number> {
    return this.agentStatus$.pipe(
      map(statuses => {
        const processingAgents = Object.values(statuses)
          .filter(agent => agent.status === 'processing');
        
        if (processingAgents.length === 0) return 0;

        // Simple estimation based on current progress
        const avgProgress = processingAgents.reduce((sum, agent) => sum + agent.progress, 0) / processingAgents.length;
        const remainingProgress = 100 - avgProgress;
        
        // Estimate 30 seconds per 10% progress
        return Math.round((remainingProgress / 10) * 30);
      })
    );
  }
}
