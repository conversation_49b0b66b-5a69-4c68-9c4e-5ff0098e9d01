import { Injectable, inject } from '@angular/core';
import { Firestore, doc, getDoc, setDoc } from '@angular/fire/firestore';
import { Auth } from '@angular/fire/auth';
import { Observable, from, map, BehaviorSubject, catchError, of } from 'rxjs';

export interface UserAPIKeys {
  groq?: string;
  deepgram?: string;
  serpapi?: string;
  lastUpdated?: Date;
}

export interface APIKeyValidation {
  valid: boolean;
  error?: string;
  service?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ApiKeyService {
  private firestore = inject(Firestore);
  private auth = inject(Auth);
  
  private apiKeysSubject = new BehaviorSubject<UserAPIKeys | null>(null);
  public apiKeys$ = this.apiKeysSubject.asObservable();
  
  private validationCache = new Map<string, { valid: boolean; timestamp: number }>();
  private readonly VALIDATION_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    // Load API keys when user authentication state changes
    this.auth.onAuthStateChanged(user => {
      if (user) {
        this.loadUserApiKeys();
      } else {
        this.apiKeysSubject.next(null);
      }
    });
  }

  /**
   * Save user API keys to Firestore
   */
  async saveApiKeys(keys: UserAPIKeys): Promise<void> {
    const user = this.auth.currentUser;
    if (!user) throw new Error('User not authenticated');

    const keysWithTimestamp = {
      ...keys,
      lastUpdated: new Date()
    };

    const userDoc = doc(this.firestore, 'users', user.uid);
    await setDoc(userDoc, { apiKeys: keysWithTimestamp }, { merge: true });
    
    // Update local state
    this.apiKeysSubject.next(keysWithTimestamp);
    
    // Clear validation cache when keys are updated
    this.clearValidationCache();
  }

  /**
   * Get user API keys from Firestore
   */
  getUserApiKeys(): Observable<UserAPIKeys | null> {
    return this.apiKeys$;
  }

  /**
   * Load API keys from Firestore
   */
  private async loadUserApiKeys(): Promise<void> {
    const user = this.auth.currentUser;
    if (!user) return;

    try {
      const userDoc = doc(this.firestore, 'users', user.uid);
      const docSnap = await getDoc(userDoc);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        const apiKeys = data?.apiKeys || null;
        this.apiKeysSubject.next(apiKeys);
      } else {
        this.apiKeysSubject.next(null);
      }
    } catch (error) {
      console.error('Error loading API keys:', error);
      this.apiKeysSubject.next(null);
    }
  }

  /**
   * Validate API key for a specific service
   */
  async validateApiKey(service: 'groq' | 'deepgram' | 'serpapi', key: string): Promise<APIKeyValidation> {
    if (!key || key.trim().length === 0) {
      return { valid: false, error: 'API key is required', service };
    }

    // Check cache first
    const cacheKey = `${service}:${key.substring(0, 10)}`;
    const cached = this.validationCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.VALIDATION_CACHE_TTL) {
      return { valid: cached.valid, service };
    }

    try {
      let isValid = false;
      let error = '';

      switch (service) {
        case 'groq':
          isValid = await this.validateGroqKey(key);
          if (!isValid) error = 'Invalid Groq API key or insufficient permissions';
          break;
        case 'deepgram':
          isValid = await this.validateDeepgramKey(key);
          if (!isValid) error = 'Invalid Deepgram API key or insufficient permissions';
          break;
        case 'serpapi':
          isValid = await this.validateSerpApiKey(key);
          if (!isValid) error = 'Invalid SerpAPI key or insufficient permissions';
          break;
        default:
          return { valid: false, error: 'Unknown service', service };
      }

      // Cache the result
      this.validationCache.set(cacheKey, { valid: isValid, timestamp: Date.now() });

      return { valid: isValid, error: isValid ? undefined : error, service };
    } catch (error) {
      console.error(`Error validating ${service} API key:`, error);
      return { 
        valid: false, 
        error: `Failed to validate ${service} API key: ${error}`, 
        service 
      };
    }
  }

  /**
   * Validate all user's API keys
   */
  async validateAllKeys(): Promise<Record<string, APIKeyValidation>> {
    const currentKeys = this.apiKeysSubject.value;
    if (!currentKeys) {
      return {};
    }

    const validations: Record<string, APIKeyValidation> = {};

    if (currentKeys.groq) {
      validations.groq = await this.validateApiKey('groq', currentKeys.groq);
    }

    if (currentKeys.deepgram) {
      validations.deepgram = await this.validateApiKey('deepgram', currentKeys.deepgram);
    }

    if (currentKeys.serpapi) {
      validations.serpapi = await this.validateApiKey('serpapi', currentKeys.serpapi);
    }

    return validations;
  }

  /**
   * Check if required API keys are present
   */
  hasRequiredKeys(): boolean {
    const keys = this.apiKeysSubject.value;
    return !!(keys?.groq && keys?.deepgram);
  }

  /**
   * Get API key for a specific service
   */
  getApiKey(service: 'groq' | 'deepgram' | 'serpapi'): string | null {
    const keys = this.apiKeysSubject.value;
    return keys?.[service] || null;
  }

  /**
   * Clear validation cache
   */
  private clearValidationCache(): void {
    this.validationCache.clear();
  }

  /**
   * Validate Groq API key
   */
  private async validateGroqKey(key: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.groq.com/openai/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${key}`,
          'Content-Type': 'application/json'
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Groq API validation error:', error);
      return false;
    }
  }

  /**
   * Validate Deepgram API key
   */
  private async validateDeepgramKey(key: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.deepgram.com/v1/projects', {
        method: 'GET',
        headers: {
          'Authorization': `Token ${key}`,
          'Content-Type': 'application/json'
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Deepgram API validation error:', error);
      return false;
    }
  }

  /**
   * Validate SerpAPI key
   */
  private async validateSerpApiKey(key: string): Promise<boolean> {
    try {
      const response = await fetch(`https://serpapi.com/account?api_key=${key}`, {
        method: 'GET'
      });

      if (response.ok) {
        const data = await response.json();
        // Check if the response indicates a valid account
        return data && !data.error;
      }

      return false;
    } catch (error) {
      console.error('SerpAPI validation error:', error);
      return false;
    }
  }

  /**
   * Get API key status summary
   */
  getKeyStatus(): { hasRequired: boolean; missing: string[]; optional: string[] } {
    const keys = this.apiKeysSubject.value;
    const missing: string[] = [];
    const optional: string[] = [];

    if (!keys?.groq) missing.push('Groq');
    if (!keys?.deepgram) missing.push('Deepgram');
    if (!keys?.serpapi) optional.push('SerpAPI');

    return {
      hasRequired: missing.length === 0,
      missing,
      optional
    };
  }

  /**
   * Remove API key for a specific service
   */
  async removeApiKey(service: 'groq' | 'deepgram' | 'serpapi'): Promise<void> {
    const currentKeys = this.apiKeysSubject.value;
    if (!currentKeys) return;

    const updatedKeys = { ...currentKeys };
    delete updatedKeys[service];

    await this.saveApiKeys(updatedKeys);
  }

  /**
   * Check if API keys are configured
   */
  areKeysConfigured(): boolean {
    const keys = this.apiKeysSubject.value;
    return !!(keys && (keys.groq || keys.deepgram || keys.serpapi));
  }

  /**
   * Get masked API key for display
   */
  getMaskedKey(key: string): string {
    if (!key || key.length < 8) return '••••••••';
    return key.substring(0, 4) + '••••••••' + key.substring(key.length - 4);
  }
}
