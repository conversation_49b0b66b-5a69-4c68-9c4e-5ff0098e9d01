import { NextRequest } from 'next/server';
import { 
  handleC<PERSON>, 
  createApiResponse, 
  createErrorResponse, 
  verifyAuth, 
  verifyOwnership,
  validateRequestBody,
  getFirebaseAdmin 
} from './lib/utils/auth';
import { MemoryManager } from './lib/utils/memory-manager';
import { CoordinatorAgent } from './lib/agents/coordinator';
import { ChatRequest, ValidationError } from './lib/types';

export default async function handler(req: NextRequest) {
  // Handle CORS preflight
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  const origin = req.headers.get('origin');

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createErrorResponse('Method not allowed', 405, origin);
    }

    // Verify authentication
    const authContext = await verifyAuth(req);

    // Validate request body
    const { sessionId, message, userId } = await validateRequestBody<ChatRequest>(
      req,
      ['sessionId', 'message', 'userId']
    );

    // Verify user owns this session
    verifyOwnership(authContext, userId);

    // Get Firebase admin instances
    const { firestore } = getFirebaseAdmin();

    // Verify session exists and user owns it
    const sessionDoc = await firestore
      .collection('sessions')
      .doc(sessionId)
      .get();

    if (!sessionDoc.exists) {
      throw new ValidationError('Session not found');
    }

    const sessionData = sessionDoc.data();
    if (!sessionData) {
      throw new ValidationError('Session data not found');
    }

    // Verify ownership again from session data
    verifyOwnership(authContext, sessionData.userId);

    // Get or create agent context
    let agentContext = MemoryManager.getContext(sessionId);
    if (!agentContext) {
      agentContext = MemoryManager.createInitialContext(sessionId, userId);
    }

    // Store the user message in memory
    const chatHistory = MemoryManager.getMemory(sessionId, 'chatHistory') || [];
    chatHistory.push({
      id: Date.now().toString(),
      role: 'user',
      content: message,
      timestamp: new Date(),
      type: 'text'
    });
    MemoryManager.addMemory(sessionId, 'chatHistory', chatHistory);

    // Update session status
    await firestore
      .collection('sessions')
      .doc(sessionId)
      .update({
        status: 'processing',
        updatedAt: new Date(),
        chatHistory: chatHistory
      });

    // Process the message through the coordinator
    const coordinator = CoordinatorAgent.getInstance();
    const result = await coordinator.orchestrateWorkflow(sessionId, message, false);

    // Create AI response message
    const aiResponse = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: result.message,
      timestamp: new Date(),
      type: 'text'
    };

    // Add AI response to chat history
    chatHistory.push(aiResponse);
    MemoryManager.addMemory(sessionId, 'chatHistory', chatHistory);

    // Update session with results
    const updateData: any = {
      status: result.success ? 'ready' : 'error',
      updatedAt: new Date(),
      chatHistory: chatHistory
    };

    if (result.updatedDescription) {
      updateData.description = result.updatedDescription;
      MemoryManager.addMemory(sessionId, 'currentDescription', result.updatedDescription);
    }

    await firestore
      .collection('sessions')
      .doc(sessionId)
      .update(updateData);

    // Prepare response data
    const responseData = {
      sessionId,
      message: result.message,
      success: result.success,
      nextAgent: result.nextAgent,
      shouldAskUser: result.shouldAskUser,
      chatHistory: chatHistory,
      agentContext: {
        sessionId: agentContext.sessionId,
        userId: agentContext.userId,
        memoriesCount: Object.keys(agentContext.memories).length,
        agentStatusesCount: Object.keys(agentContext.agentStatuses).length,
      },
      workflowStatus: coordinator.getWorkflowStatus(sessionId),
    };

    if (result.updatedDescription) {
      responseData.updatedDescription = result.updatedDescription;
    }

    return createApiResponse(
      {
        success: result.success,
        data: responseData,
        message: result.message,
      },
      200,
      origin
    );

  } catch (error) {
    console.error('Chat error:', error);

    if (error instanceof ValidationError) {
      return createErrorResponse(error.message, error.statusCode, origin);
    }

    return createErrorResponse(
      error instanceof Error ? error.message : 'Chat processing failed',
      500,
      origin
    );
  }
}
