import { NextRequest } from 'next/server';
import { handleCors, createApiResponse } from './lib/utils/auth';
import { MemoryManager } from './lib/utils/memory-manager';

export default async function handler(req: NextRequest) {
  // Handle CORS preflight
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    const origin = req.headers.get('origin');
    
    // Only allow GET requests
    if (req.method !== 'GET') {
      return createApiResponse(
        { success: false, error: 'Method not allowed' },
        405,
        origin
      );
    }

    // Get memory stats
    const memoryStats = MemoryManager.getStats();
    
    // Check environment variables
    const envCheck = {
      hasGroqKey: !!process.env.GROQ_API_KEY,
      hasFirebaseConfig: !!(
        process.env.FIREBASE_PROJECT_ID &&
        process.env.FIREBASE_CLIENT_EMAIL &&
        process.env.FIREBASE_PRIVATE_KEY
      ),
      hasDeepgramKey: !!process.env.DEEPGRAM_API_KEY,
      hasSerpApiKey: !!process.env.SERPAPI_KEY,
    };

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      memory: {
        activeSessions: memoryStats.totalSessions,
        memoryUsage: `${Math.round(memoryStats.totalMemorySize / 1024)} KB`,
        oldestSession: memoryStats.oldestSession,
        newestSession: memoryStats.newestSession,
      },
      services: {
        groq: envCheck.hasGroqKey ? 'configured' : 'missing',
        firebase: envCheck.hasFirebaseConfig ? 'configured' : 'missing',
        deepgram: envCheck.hasDeepgramKey ? 'configured' : 'missing',
        serpapi: envCheck.hasSerpApiKey ? 'configured' : 'missing',
      },
      uptime: process.uptime ? `${Math.round(process.uptime())}s` : 'unknown',
    };

    return createApiResponse(
      {
        success: true,
        data: healthData,
      },
      200,
      origin
    );
  } catch (error) {
    console.error('Health check failed:', error);
    
    return createApiResponse(
      {
        success: false,
        error: 'Health check failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500,
      req.headers.get('origin')
    );
  }
}
