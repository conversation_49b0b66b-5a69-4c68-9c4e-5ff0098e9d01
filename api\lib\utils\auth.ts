import { NextRequest } from 'next/server';
import * as admin from 'firebase-admin';
import { AuthenticationError, AuthorizationError } from '../types';

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
    databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com`,
    storageBucket: `${process.env.FIREBASE_PROJECT_ID}.appspot.com`,
  });
}

export interface AuthContext {
  uid: string;
  email?: string;
  emailVerified?: boolean;
  customClaims?: Record<string, any>;
}

/**
 * Verify Firebase ID token from request headers
 */
export async function verifyAuth(request: NextRequest): Promise<AuthContext> {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Missing or invalid authorization header');
    }

    // Extract token
    const idToken = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify token with Firebase Admin
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    return {
      uid: decodedToken.uid,
      email: decodedToken.email,
      emailVerified: decodedToken.email_verified,
      customClaims: decodedToken,
    };
  } catch (error) {
    console.error('Auth verification failed:', error);
    
    if (error instanceof AuthenticationError) {
      throw error;
    }
    
    // Handle Firebase Auth errors
    if (error.code === 'auth/id-token-expired') {
      throw new AuthenticationError('Token expired');
    } else if (error.code === 'auth/id-token-revoked') {
      throw new AuthenticationError('Token revoked');
    } else if (error.code === 'auth/invalid-id-token') {
      throw new AuthenticationError('Invalid token');
    }
    
    throw new AuthenticationError('Authentication failed');
  }
}

/**
 * Verify user owns the resource
 */
export function verifyOwnership(authContext: AuthContext, resourceUserId: string): void {
  if (authContext.uid !== resourceUserId) {
    throw new AuthorizationError('User can only access their own resources');
  }
}

/**
 * Get Firebase Admin instances
 */
export function getFirebaseAdmin() {
  return {
    auth: admin.auth(),
    firestore: admin.firestore(),
    storage: admin.storage(),
  };
}

/**
 * CORS headers for API responses
 */
export function getCorsHeaders(origin?: string) {
  return {
    'Access-Control-Allow-Origin': origin || '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400',
  };
}

/**
 * Handle CORS preflight requests
 */
export function handleCors(request: NextRequest) {
  const origin = request.headers.get('origin');
  
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: getCorsHeaders(origin),
    });
  }
  
  return null;
}

/**
 * Create API response with CORS headers
 */
export function createApiResponse(
  data: any,
  status: number = 200,
  origin?: string
) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...getCorsHeaders(origin),
    },
  });
}

/**
 * Create error response
 */
export function createErrorResponse(
  error: Error | string,
  status: number = 500,
  origin?: string
) {
  const message = typeof error === 'string' ? error : error.message;
  const code = error instanceof Error && 'code' in error ? error.code : undefined;
  
  return createApiResponse(
    {
      success: false,
      error: message,
      code,
    },
    status,
    origin
  );
}

/**
 * Validate request body
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  requiredFields: string[]
): Promise<T> {
  try {
    const body = await request.json();
    
    // Check required fields
    for (const field of requiredFields) {
      if (!(field in body) || body[field] === undefined || body[field] === null) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    return body as T;
  } catch (error) {
    if (error instanceof SyntaxError) {
      throw new Error('Invalid JSON in request body');
    }
    throw error;
  }
}
