import { NextRequest } from 'next/server';
import { 
  handleC<PERSON>, 
  createApiResponse, 
  createErrorResponse, 
  verifyAuth, 
  verifyOwnership,
  getFirebaseAdmin 
} from './lib/utils/auth';
import { ValidationError } from './lib/types';

export default async function handler(req: NextRequest) {
  // Handle CORS preflight
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  const origin = req.headers.get('origin');

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createErrorResponse('Method not allowed', 405, origin);
    }

    // Verify authentication
    const authContext = await verifyAuth(req);

    // Parse form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const sessionId = formData.get('sessionId') as string;
    const userId = formData.get('userId') as string;
    const fileType = formData.get('fileType') as string || 'audio';

    if (!file || !sessionId || !userId) {
      throw new ValidationError('Missing required fields: file, sessionId, userId');
    }

    // Verify user owns this session
    verifyOwnership(authContext, userId);

    // Get Firebase admin instances
    const { storage } = getFirebaseAdmin();

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Generate file path
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop() || 'bin';
    const fileName = `${sessionId}_${timestamp}_${crypto.randomUUID()}.${fileExtension}`;
    const filePath = `${fileType}-files/${userId}/${sessionId}/${fileName}`;

    // Upload to Firebase Storage
    const bucket = storage.bucket();
    const fileRef = bucket.file(filePath);
    
    await fileRef.save(buffer, {
      metadata: {
        contentType: file.type,
        metadata: {
          sessionId,
          userId,
          originalName: file.name,
          uploadedAt: new Date().toISOString(),
        }
      }
    });

    // Make file publicly readable (optional, adjust based on your needs)
    await fileRef.makePublic();

    // Get download URL
    const downloadURL = `https://storage.googleapis.com/${bucket.name}/${filePath}`;

    return createApiResponse(
      {
        success: true,
        data: {
          fileName,
          filePath,
          downloadURL,
          size: file.size,
          type: file.type,
        },
      },
      200,
      origin
    );

  } catch (error) {
    console.error('Upload error:', error);

    if (error instanceof ValidationError) {
      return createErrorResponse(error.message, error.statusCode, origin);
    }

    return createErrorResponse(
      error instanceof Error ? error.message : 'Upload failed',
      500,
      origin
    );
  }
}
