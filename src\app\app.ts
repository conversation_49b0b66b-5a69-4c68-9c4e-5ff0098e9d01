import { Component, inject, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Observable, Subscription } from 'rxjs';
import { FirebaseService } from './services/firebase.service';
import { AgentService } from './services/agent.service';
import { FileUploadService, UploadProgress } from './services/file-upload.service';
import { UploadZoneComponent, FileUploadEvent } from './components/upload-zone/upload-zone.component';
import {
  ChatMessage,
  PodcastDescription,
  ProcessingSession,
  User,
  AgentStatus
} from './models/podcast-description.model';

@Component({
  selector: 'app-root',
  imports: [CommonModule, FormsModule, UploadZoneComponent],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App implements OnInit, OnDestroy {
  private firebaseService = inject(FirebaseService);
  private agentService = inject(AgentService);
  private fileUploadService = inject(FileUploadService);

  // Observable for user authentication state
  user$: Observable<any> = this.firebaseService.user$;

  // Component state
  showUserMenu = false;
  autoMode = false;
  chatInput = '';
  chatMessages: ChatMessage[] = [];
  isProcessing = false;
  currentDescription: PodcastDescription | null = null;
  currentSession: ProcessingSession | null = null;

  // Upload modal state
  showUploadModal = false;
  selectedFile: File | null = null;
  uploadType: 'audio' | 'transcript' = 'audio';
  isUploading = false;
  isDragOver = false;
  uploadProgress: UploadProgress | null = null;
  uploadError: string | null = null;

  // Agent status tracking
  agentStatuses: Record<string, AgentStatus> = {};
  processingProgress = 0;
  estimatedTimeRemaining = 0;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  ngOnInit() {
    // Initialize with welcome message
    this.chatMessages = [
      {
        id: '1',
        role: 'assistant',
        content: 'Hello! I\'m your AI assistant. Upload a podcast audio file or transcript to get started, and I\'ll help you create an amazing YouTube description.',
        timestamp: new Date(),
        type: 'text'
      }
    ];

    // Listen for user authentication changes
    this.subscriptions.push(
      this.user$.subscribe(user => {
        if (user && !this.showUploadModal && !this.currentSession) {
          this.showUploadModal = true;
        }
      })
    );

    // Subscribe to agent status updates
    this.subscriptions.push(
      this.agentService.agentStatus$.subscribe(statuses => {
        this.agentStatuses = statuses;
      })
    );

    // Subscribe to upload progress
    this.subscriptions.push(
      this.fileUploadService.getUploadProgress().subscribe(progress => {
        this.uploadProgress = progress;
      })
    );

    // Subscribe to processing progress
    this.subscriptions.push(
      this.agentService.getProgressSummary().subscribe(progress => {
        this.processingProgress = progress.overallProgress;

        // Update chat with progress messages
        if (progress.processingAgents.length > 0) {
          const currentAgent = progress.processingAgents[0];
          this.updateProcessingMessage(currentAgent.name, currentAgent.message, currentAgent.progress);
        }
      })
    );

    // Subscribe to estimated time remaining
    this.subscriptions.push(
      this.agentService.getEstimatedTimeRemaining().subscribe(time => {
        this.estimatedTimeRemaining = time;
      })
    );
  }

  ngOnDestroy() {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.agentService.reset();
  }

  // Authentication methods
  signInWithGoogle() {
    this.firebaseService.signInWithGoogle().subscribe({
      next: (user) => {
        if (user) {
          console.log('Sign in successful:', user);
        }
      },
      error: (error) => {
        console.error('Sign in failed:', error);
        this.addSystemMessage('Sign in failed. Please try again.');
      }
    });
  }

  async signOut() {
    try {
      await this.firebaseService.signOut();
      this.resetState();
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  }

  // UI methods
  toggleUserMenu() {
    this.showUserMenu = !this.showUserMenu;
  }

  toggleDarkMode() {
    // TODO: Implement dark mode toggle
    console.log('Dark mode toggle clicked');
  }

  // Chat methods
  sendMessage() {
    if (!this.chatInput.trim() || this.isProcessing) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: this.chatInput.trim(),
      timestamp: new Date(),
      type: 'text'
    };

    this.chatMessages.push(userMessage);
    this.chatInput = '';
    this.isProcessing = true;

    // TODO: Send message to AI agent
    this.processUserMessage(userMessage.content);
  }

  private async processUserMessage(message: string) {
    if (!this.currentSession) {
      this.addSystemMessage('Please upload an audio file first.');
      this.isProcessing = false;
      return;
    }

    try {
      const user = this.firebaseService.getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Send message to agent service
      const response = await this.agentService.sendChatMessage({
        sessionId: this.currentSession.id,
        message,
        userId: user.uid
      }).toPromise();

      if (response.success) {
        const aiResponse: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: response.message,
          timestamp: new Date(),
          type: 'text'
        };

        this.chatMessages.push(aiResponse);

        // Update description if provided
        if (response.updatedDescription) {
          this.currentDescription = { ...this.currentDescription, ...response.updatedDescription };
        }
      } else {
        this.addSystemMessage('Sorry, I encountered an error processing your message. Please try again.');
      }
    } catch (error) {
      console.error('Error processing message:', error);
      this.addSystemMessage('Sorry, I encountered an error. Please try again.');
    } finally {
      this.isProcessing = false;
    }
  }



  // File upload methods
  onFileSelected(event: FileUploadEvent) {
    this.selectedFile = event.file;
    this.uploadType = event.uploadType;
    this.uploadError = null;

    // Show any validation warnings
    if (event.validation.warnings) {
      event.validation.warnings.forEach(warning => {
        this.addSystemMessage(warning, 'warning');
      });
    }
  }

  onUploadTypeChanged(type: 'audio' | 'transcript') {
    this.uploadType = type;
    this.selectedFile = null;
    this.uploadError = null;
  }

  async uploadFile() {
    if (!this.selectedFile) return;

    this.isUploading = true;
    this.uploadError = null;

    try {
      const user = this.firebaseService.getCurrentUser();
      if (!user) throw new Error('User not authenticated');

      // Create new session
      const sessionData: Omit<ProcessingSession, 'id'> = {
        userId: user.uid,
        audioFileUrl: '',
        transcript: '',
        description: {} as PodcastDescription,
        chatHistory: [],
        status: 'uploading',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const sessionId = await this.firebaseService.createSession(sessionData);

      // Upload file using the new upload service
      const fileUrl = await this.fileUploadService.uploadFile(
        this.selectedFile,
        sessionId,
        user.uid,
        this.uploadType
      );

      // Process transcript file if needed
      let transcript = '';
      if (this.uploadType === 'transcript') {
        transcript = await this.fileUploadService.processTranscriptFile(this.selectedFile);
      }

      // Update session with file URL and transcript
      await this.firebaseService.updateSession(sessionId, {
        audioFileUrl: fileUrl,
        transcript: transcript,
        status: this.uploadType === 'transcript' ? 'processing' : 'transcribing'
      });

      // Store filename before closing modal
      const fileName = this.selectedFile.name;
      this.closeUploadModal();
      this.addSystemMessage(`File uploaded successfully! Starting transcription of "${fileName}"`);

      // TODO: Start processing pipeline
      this.startProcessingPipeline(sessionId);

    } catch (error) {
      console.error('Upload failed:', error);
      this.addSystemMessage('Upload failed. Please try again.');
    } finally {
      this.isUploading = false;
    }
  }

  private async startProcessingPipeline(sessionId: string) {
    try {
      const user = this.firebaseService.getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      this.addSystemMessage('Starting AI processing pipeline...');

      // Get the session data
      const session = await this.firebaseService.getSession(sessionId).toPromise();
      if (!session) {
        throw new Error('Session not found');
      }

      this.currentSession = session;

      // Start processing with agent service
      const response = await this.agentService.startProcessing({
        sessionId,
        audioFileUrl: session.audioFileUrl,
        userId: user.uid,
        autoMode: this.autoMode
      }).toPromise();

      if (response.success) {
        this.addSystemMessage('Processing started! I\'ll update you on progress...');

        // Subscribe to completion
        this.subscriptions.push(
          this.agentService.isProcessingComplete().subscribe(isComplete => {
            if (isComplete) {
              this.onProcessingComplete(sessionId);
            }
          })
        );
      } else {
        this.addSystemMessage(`Processing failed: ${response.message}`);
      }

    } catch (error) {
      console.error('Error starting processing:', error);
      this.addSystemMessage('Failed to start processing. Please try again.');
    }
  }

  private async onProcessingComplete(sessionId: string) {
    try {
      // Get the updated session with results
      const session = await this.firebaseService.getSession(sessionId).toPromise();
      if (session && session.description) {
        this.currentDescription = session.description;
        this.addSystemMessage('Processing complete! Your YouTube description is ready. You can now chat with me to make any changes.');
      }
    } catch (error) {
      console.error('Error getting final results:', error);
      this.addSystemMessage('Processing completed, but there was an error retrieving the results.');
    }
  }

  private updateProcessingMessage(agentName: string, message: string, progress: number) {
    const agentDisplayNames: Record<string, string> = {
      'transcriber': 'Audio Transcription',
      'topicExtractor': 'Topic Analysis',
      'linkFinder': 'Resource Discovery',
      'profileFinder': 'Profile Search',
      'descriptionWriter': 'Description Generation',
      'editor': 'Content Editing'
    };

    const displayName = agentDisplayNames[agentName] || agentName;
    const progressText = progress > 0 ? ` (${Math.round(progress)}%)` : '';

    // Update or add processing message
    const processingMessageId = 'processing-status';
    const existingIndex = this.chatMessages.findIndex(msg => msg.id === processingMessageId);

    const statusMessage: ChatMessage = {
      id: processingMessageId,
      role: 'assistant',
      content: `${displayName}: ${message}${progressText}`,
      timestamp: new Date(),
      type: 'system'
    };

    if (existingIndex >= 0) {
      this.chatMessages[existingIndex] = statusMessage;
    } else {
      this.chatMessages.push(statusMessage);
    }
  }

  private generateSampleDescription() {
    // Sample description for demonstration
    this.currentDescription = {
      guestBio: 'John Doe is a renowned tech entrepreneur and author of "The Future of AI".',
      hostBio: 'Jane Smith hosts the popular Tech Talk podcast, interviewing industry leaders.',
      overview: 'In this episode, we dive deep into the future of artificial intelligence and its impact on society.',
      keyTopics: [
        'The current state of AI technology',
        'Ethical considerations in AI development',
        'Future predictions for AI adoption',
        'Impact on job markets and society'
      ],
      resources: [
        {
          label: 'The Future of AI (Book)',
          url: 'https://example.com/book',
          type: 'book',
          confidence: 0.95,
          extractedFrom: 'Mentioned at 15:30'
        },
        {
          label: 'OpenAI Website',
          url: 'https://openai.com',
          type: 'website',
          confidence: 0.90,
          extractedFrom: 'Discussed at 22:15'
        }
      ],
      timestamps: [
        { time: '00:00', label: 'Introduction and guest welcome', confidence: 0.95 },
        { time: '05:30', label: 'Current state of AI technology', confidence: 0.90 },
        { time: '15:30', label: 'Book discussion: The Future of AI', confidence: 0.92 },
        { time: '25:45', label: 'Ethical considerations in AI', confidence: 0.88 },
        { time: '35:20', label: 'Future predictions and wrap-up', confidence: 0.85 }
      ],
      extended: 'This comprehensive discussion covers the evolution of artificial intelligence from its early days to current breakthroughs. John shares insights from his latest book and discusses the ethical implications of AI development. The conversation explores how AI will reshape industries and what individuals can do to prepare for an AI-driven future.',
      seo: {
        keywords: ['artificial intelligence', 'AI future', 'tech entrepreneur', 'machine learning', 'AI ethics'],
        hashtags: ['#AI', '#TechTalk', '#FutureOfWork', '#MachineLearning', '#Innovation'],
        suggestedTitle: 'The Future of AI: Insights from Tech Entrepreneur John Doe'
      },
      lastModified: new Date(),
      version: 1
    };
  }

  closeUploadModal() {
    this.showUploadModal = false;
    this.selectedFile = null;
    this.isDragOver = false;
  }

  // Description actions
  copyDescription() {
    if (!this.currentDescription) return;

    const formattedDescription = this.formatDescriptionForCopy();
    navigator.clipboard.writeText(formattedDescription).then(() => {
      this.addSystemMessage('Description copied to clipboard!');
    }).catch(() => {
      this.addSystemMessage('Failed to copy description. Please try again.');
    });
  }

  exportDescription() {
    if (!this.currentDescription) return;

    const formattedDescription = this.formatDescriptionForCopy();
    const blob = new Blob([formattedDescription], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'youtube-description.txt';
    a.click();
    window.URL.revokeObjectURL(url);

    this.addSystemMessage('Description exported successfully!');
  }

  private formatDescriptionForCopy(): string {
    if (!this.currentDescription) return '';

    let formatted = '';

    // Overview
    formatted += `${this.currentDescription.overview}\n\n`;

    // Guest Bio
    if (this.currentDescription.guestBio) {
      formatted += `🎤 Guest: ${this.currentDescription.guestBio}\n\n`;
    }

    // Host Bio
    if (this.currentDescription.hostBio) {
      formatted += `🎙️ Host: ${this.currentDescription.hostBio}\n\n`;
    }

    // Key Topics
    if (this.currentDescription.keyTopics?.length) {
      formatted += `📋 Key Topics:\n`;
      this.currentDescription.keyTopics.forEach(topic => {
        formatted += `• ${topic}\n`;
      });
      formatted += '\n';
    }

    // Timestamps
    if (this.currentDescription.timestamps?.length) {
      formatted += `⏰ Timestamps:\n`;
      this.currentDescription.timestamps.forEach(timestamp => {
        formatted += `${timestamp.time} - ${timestamp.label}\n`;
      });
      formatted += '\n';
    }

    // Resources
    if (this.currentDescription.resources?.length) {
      formatted += `🔗 Resources:\n`;
      this.currentDescription.resources.forEach(resource => {
        formatted += `• ${resource.label}: ${resource.url}\n`;
      });
      formatted += '\n';
    }

    // Extended Summary
    if (this.currentDescription.extended) {
      formatted += `📝 Extended Summary:\n${this.currentDescription.extended}\n\n`;
    }

    // SEO
    if (this.currentDescription.seo) {
      if (this.currentDescription.seo.hashtags?.length) {
        formatted += `🏷️ Tags: ${this.currentDescription.seo.hashtags.join(' ')}\n`;
      }
    }

    return formatted;
  }

  private resetState() {
    this.chatMessages = [
      {
        id: '1',
        role: 'assistant',
        content: 'Hello! I\'m your AI assistant. Upload a podcast audio file to get started.',
        timestamp: new Date(),
        type: 'text'
      }
    ];
    this.currentDescription = null;
    this.currentSession = null;
    this.showUserMenu = false;
    this.autoMode = false;
    this.chatInput = '';
    this.isProcessing = false;
  }

  private addSystemMessage(content: string, type: 'text' | 'system' | 'error' | 'warning' = 'system') {
    this.chatMessages.push({
      id: Date.now().toString(),
      role: 'assistant',
      content,
      timestamp: new Date(),
      type
    });
  }
}
