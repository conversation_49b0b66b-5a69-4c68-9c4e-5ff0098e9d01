<!-- <PERSON>ed<PERSON> AI - Podcast to YouTube Description Generator -->
<div class="min-h-screen bg-gray-50">
  <!-- Top Navigation Bar -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo and Title -->
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <h1 class="text-xl font-semibold text-gray-900">Pedma AI</h1>
          <span class="text-sm text-gray-500">Podcast to YouTube Description</span>
        </div>

        <!-- User Menu -->
        <div class="flex items-center space-x-4">
          <!-- Dark Mode Toggle -->
          <button 
            class="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
            (click)="toggleDarkMode()"
            title="Toggle dark mode">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
            </svg>
          </button>

          <!-- User Profile -->
          <div class="relative" *ngIf="user$ | async as user; else loginButton">
            <button 
              class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              (click)="toggleUserMenu()">
              <img 
                [src]="user.photoURL || '/assets/default-avatar.png'" 
                [alt]="user.displayName || 'User'"
                class="w-8 h-8 rounded-full">
              <span class="text-sm font-medium text-gray-700">{{ user.displayName || user.email }}</span>
            </button>
            
            <!-- User Dropdown Menu -->
            <div 
              *ngIf="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">API Keys</a>
              <hr class="my-1">
              <button 
                (click)="signOut()"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                Sign Out
              </button>
            </div>
          </div>

          <!-- Login Button -->
          <ng-template #loginButton>
            <button 
              (click)="signInWithGoogle()"
              class="btn-primary">
              Sign In with Google
            </button>
          </ng-template>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content Area -->
  <div class="flex h-[calc(100vh-4rem)]" *ngIf="user$ | async; else welcomeScreen">
    <!-- Left Panel - Chat Interface -->
    <div class="w-2/5 bg-white border-r border-gray-200 flex flex-col">
      <!-- Chat Header -->
      <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">AI Assistant</h2>
        <p class="text-sm text-gray-600">Chat with AI to refine your description</p>

        <!-- Processing Progress -->
        <div *ngIf="processingProgress > 0 && processingProgress < 100" class="mt-3">
          <div class="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Processing...</span>
            <span>{{ processingProgress | number:'1.0-0' }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              [style.width.%]="processingProgress">
            </div>
          </div>
          <div *ngIf="estimatedTimeRemaining > 0" class="text-xs text-gray-500 mt-1">
            Estimated time remaining: {{ estimatedTimeRemaining }}s
          </div>
        </div>

        <!-- Auto Mode Toggle -->
        <div class="mt-3 flex items-center space-x-3">
          <label class="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              [(ngModel)]="autoMode"
              class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
            <span class="text-sm font-medium text-gray-700">Auto Mode</span>
          </label>
          <div class="text-xs text-gray-500">
            Automatically complete processing without prompts
          </div>
        </div>
      </div>

      <!-- Chat Messages -->
      <div class="flex-1 overflow-y-auto p-4 space-y-4">
        <div *ngFor="let message of chatMessages" 
             [ngClass]="{
               'flex justify-end': message.role === 'user',
               'flex justify-start': message.role === 'assistant'
             }">
          <div [ngClass]="{
                 'bg-blue-600 text-white': message.role === 'user',
                 'bg-gray-100 text-gray-900': message.role === 'assistant'
               }"
               class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
            <p class="text-sm">{{ message.content }}</p>
            <p class="text-xs opacity-75 mt-1">{{ message.timestamp | date:'short' }}</p>
          </div>
        </div>
        
        <!-- Typing Indicator -->
        <div *ngIf="isProcessing" class="flex justify-start">
          <div class="bg-gray-100 text-gray-900 max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Input -->
      <div class="p-4 border-t border-gray-200">
        <div class="flex space-x-2">
          <input 
            type="text" 
            [(ngModel)]="chatInput"
            (keyup.enter)="sendMessage()"
            placeholder="Type your message..."
            class="input-field flex-1"
            [disabled]="isProcessing">
          <button 
            (click)="sendMessage()"
            [disabled]="!chatInput.trim() || isProcessing"
            class="btn-primary px-4 py-2">
            Send
          </button>
        </div>
      </div>
    </div>

    <!-- Right Panel - Description Editor -->
    <div class="flex-1 bg-white flex flex-col">
      <!-- Editor Header -->
      <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <div>
          <h2 class="text-lg font-semibold text-gray-900">YouTube Description</h2>
          <p class="text-sm text-gray-600">Live preview of your generated description</p>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex space-x-2">
          <button 
            (click)="copyDescription()"
            class="btn-secondary">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"/>
              <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"/>
            </svg>
            Copy
          </button>
          <button 
            (click)="exportDescription()"
            class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
            Export
          </button>
        </div>
      </div>

      <!-- Description Content -->
      <div class="flex-1 overflow-y-auto p-6">
        <div *ngIf="!currentDescription" class="text-center py-12">
          <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Description Yet</h3>
          <p class="text-gray-600">Upload an audio file to get started</p>
        </div>

        <!-- Description Sections -->
        <div *ngIf="currentDescription" class="space-y-6">
          <!-- Overview -->
          <section class="card">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Overview</h3>
            <p class="text-gray-700 leading-relaxed">{{ currentDescription.overview }}</p>
          </section>

          <!-- Guest Bio -->
          <section class="card" *ngIf="currentDescription.guestBio">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Guest Bio</h3>
            <p class="text-gray-700 leading-relaxed">{{ currentDescription.guestBio }}</p>
          </section>

          <!-- Host Bio -->
          <section class="card" *ngIf="currentDescription.hostBio">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Host Bio</h3>
            <p class="text-gray-700 leading-relaxed">{{ currentDescription.hostBio }}</p>
          </section>

          <!-- Key Topics -->
          <section class="card" *ngIf="currentDescription.keyTopics?.length">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Key Topics</h3>
            <ul class="space-y-2">
              <li *ngFor="let topic of currentDescription.keyTopics" 
                  class="flex items-start space-x-2">
                <span class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                <span class="text-gray-700">{{ topic }}</span>
              </li>
            </ul>
          </section>

          <!-- Resources -->
          <section class="card" *ngIf="currentDescription.resources?.length">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Resources & Links</h3>
            <ul class="space-y-2">
              <li *ngFor="let resource of currentDescription.resources" 
                  class="flex items-center space-x-2">
                <a [href]="resource.url" 
                   target="_blank"
                   class="text-blue-600 hover:text-blue-800 underline">
                  {{ resource.label }}
                </a>
                <span class="text-xs text-gray-500 capitalize">({{ resource.type }})</span>
              </li>
            </ul>
          </section>

          <!-- Timestamps -->
          <section class="card" *ngIf="currentDescription.timestamps?.length">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Timestamps</h3>
            <ul class="space-y-2">
              <li *ngFor="let timestamp of currentDescription.timestamps" 
                  class="flex items-start space-x-3">
                <span class="text-blue-600 font-mono text-sm font-medium">{{ timestamp.time }}</span>
                <span class="text-gray-700">{{ timestamp.label }}</span>
              </li>
            </ul>
          </section>

          <!-- Extended Summary -->
          <section class="card" *ngIf="currentDescription.extended">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Extended Summary</h3>
            <p class="text-gray-700 leading-relaxed">{{ currentDescription.extended }}</p>
          </section>

          <!-- SEO -->
          <section class="card" *ngIf="currentDescription.seo">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">SEO & Tags</h3>
            
            <!-- Keywords -->
            <div *ngIf="currentDescription.seo.keywords?.length" class="mb-4">
              <h4 class="text-sm font-medium text-gray-900 mb-2">Keywords</h4>
              <div class="flex flex-wrap gap-2">
                <span *ngFor="let keyword of currentDescription.seo.keywords"
                      class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  {{ keyword }}
                </span>
              </div>
            </div>

            <!-- Hashtags -->
            <div *ngIf="currentDescription.seo.hashtags?.length">
              <h4 class="text-sm font-medium text-gray-900 mb-2">Hashtags</h4>
              <div class="flex flex-wrap gap-2">
                <span *ngFor="let hashtag of currentDescription.seo.hashtags"
                      class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                  {{ hashtag }}
                </span>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  </div>

  <!-- Welcome Screen for Non-Authenticated Users -->
  <ng-template #welcomeScreen>
    <div class="flex items-center justify-center min-h-[calc(100vh-4rem)]">
      <div class="text-center max-w-md mx-auto px-4">
        <div class="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Pedma AI</h2>
        <p class="text-gray-600 mb-8">Transform your podcast audio into compelling YouTube descriptions with AI-powered assistance.</p>
        <button 
          (click)="signInWithGoogle()"
          class="btn-primary text-lg px-8 py-3">
          Get Started with Google
        </button>
      </div>
    </div>
  </ng-template>
</div>

<!-- Upload Modal -->
<div *ngIf="showUploadModal"
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
     (click)="closeUploadModal()">
  <div class="bg-white rounded-xl p-6 max-w-lg w-full mx-4" (click)="$event.stopPropagation()">
    <h3 class="text-lg font-semibold text-gray-900 mb-6">Upload Content</h3>

    <!-- Upload Zone Component -->
    <app-upload-zone
      [uploadType]="uploadType"
      [disabled]="isUploading"
      (fileSelected)="onFileSelected($event)"
      (uploadTypeChanged)="onUploadTypeChanged($event)">
    </app-upload-zone>

    <!-- Upload Error -->
    <div *ngIf="uploadError"
         class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-start space-x-2">
        <svg class="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
        </svg>
        <div>
          <p class="text-sm font-medium text-red-800">Upload Failed</p>
          <p class="text-sm text-red-700">{{ uploadError }}</p>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-6 flex space-x-3">
      <button
        (click)="closeUploadModal()"
        [disabled]="isUploading"
        class="btn-secondary flex-1">
        Cancel
      </button>
      <button
        (click)="uploadFile()"
        [disabled]="!selectedFile || isUploading"
        class="btn-primary flex-1">
        <span *ngIf="!isUploading">Upload {{ uploadType === 'audio' ? 'Audio' : 'Transcript' }}</span>
        <span *ngIf="isUploading">
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Uploading...
        </span>
      </button>
    </div>
  </div>
</div>
