import { NextRequest } from 'next/server';
import { 
  handleC<PERSON>, 
  createApiResponse, 
  createErrorResponse, 
  verifyAuth, 
  verifyOwnership,
  getFirebaseAdmin 
} from './lib/utils/auth';
import { MemoryManager } from './lib/utils/memory-manager';
import { ValidationError } from './lib/types';

export default async function handler(req: NextRequest) {
  // Handle CORS preflight
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  const origin = req.headers.get('origin');

  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return createErrorResponse('Method not allowed', 405, origin);
    }

    // Verify authentication
    const authContext = await verifyAuth(req);

    // Get sessionId from query parameters
    const url = new URL(req.url);
    const sessionId = url.searchParams.get('sessionId');

    if (!sessionId) {
      throw new ValidationError('sessionId query parameter is required');
    }

    // Get Firebase admin instances
    const { firestore } = getFirebaseAdmin();

    // Get session from Firestore
    const sessionDoc = await firestore
      .collection('sessions')
      .doc(sessionId)
      .get();

    if (!sessionDoc.exists) {
      return createErrorResponse('Session not found', 404, origin);
    }

    const sessionData = sessionDoc.data();
    if (!sessionData) {
      return createErrorResponse('Session data not found', 404, origin);
    }

    // Verify user owns this session
    verifyOwnership(authContext, sessionData.userId);

    // Get agent context from memory
    const agentContext = MemoryManager.getContext(sessionId);
    const contextSummary = MemoryManager.getContextSummary(sessionId);

    // Prepare response data
    const responseData = {
      session: {
        id: sessionId,
        ...sessionData,
        updatedAt: sessionData.updatedAt?.toDate?.() || sessionData.updatedAt,
        createdAt: sessionData.createdAt?.toDate?.() || sessionData.createdAt,
      },
      agentContext: agentContext ? {
        sessionId: agentContext.sessionId,
        userId: agentContext.userId,
        memoriesCount: Object.keys(agentContext.memories).length,
        agentStatusesCount: Object.keys(agentContext.agentStatuses).length,
        createdAt: agentContext.createdAt,
        lastAccessed: agentContext.lastAccessed,
      } : null,
      agentStatuses: agentContext?.agentStatuses || {},
      contextSummary: contextSummary,
      isActive: !!agentContext,
    };

    return createApiResponse(
      {
        success: true,
        data: responseData,
      },
      200,
      origin
    );

  } catch (error) {
    console.error('Session status error:', error);

    if (error instanceof ValidationError) {
      return createErrorResponse(error.message, error.statusCode, origin);
    }

    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to get session status',
      500,
      origin
    );
  }
}
