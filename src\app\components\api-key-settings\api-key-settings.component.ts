import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ApiKeyService, UserAPIKeys, APIKeyValidation } from '../../services/api-key.service';

interface KeyField {
  service: 'groq' | 'deepgram' | 'serpapi';
  label: string;
  description: string;
  placeholder: string;
  required: boolean;
  helpUrl: string;
  value: string;
  validation: APIKeyValidation | null;
  isValidating: boolean;
  showKey: boolean;
}

@Component({
  selector: 'app-api-key-settings',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="max-w-4xl mx-auto p-6">
      <!-- Header -->
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">API Key Settings</h2>
        <p class="text-gray-600">
          Configure your API keys to enable AI-powered features. All keys are securely stored and encrypted.
        </p>
        
        <!-- Status Summary -->
        <div class="mt-4 p-4 rounded-lg" 
             [ngClass]="{
               'bg-green-50 border border-green-200': keyStatus.hasRequired,
               'bg-yellow-50 border border-yellow-200': !keyStatus.hasRequired
             }">
          <div class="flex items-start space-x-3">
            <svg *ngIf="keyStatus.hasRequired" class="w-5 h-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            <svg *ngIf="!keyStatus.hasRequired" class="w-5 h-5 text-yellow-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            <div>
              <h3 class="text-sm font-medium" 
                  [ngClass]="{
                    'text-green-900': keyStatus.hasRequired,
                    'text-yellow-900': !keyStatus.hasRequired
                  }">
                {{ keyStatus.hasRequired ? 'All Required Keys Configured' : 'Missing Required Keys' }}
              </h3>
              <p class="text-sm mt-1" 
                 [ngClass]="{
                   'text-green-700': keyStatus.hasRequired,
                   'text-yellow-700': !keyStatus.hasRequired
                 }">
                <span *ngIf="keyStatus.hasRequired">
                  Your application is ready to process audio files and generate descriptions.
                </span>
                <span *ngIf="!keyStatus.hasRequired">
                  Please configure the following required API keys: {{ keyStatus.missing.join(', ') }}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- API Key Form -->
      <form (ngSubmit)="saveKeys()" class="space-y-8">
        <!-- API Key Fields -->
        <div *ngFor="let field of keyFields" class="space-y-4">
          <div class="bg-white border border-gray-200 rounded-lg p-6">
            <!-- Field Header -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <h3 class="text-lg font-semibold text-gray-900">{{ field.label }}</h3>
                  <span *ngIf="field.required" class="text-red-500 text-sm">*</span>
                  <span *ngIf="!field.required" class="text-gray-400 text-sm">(Optional)</span>
                </div>
                <p class="text-sm text-gray-600 mt-1">{{ field.description }}</p>
              </div>
              
              <!-- Validation Status -->
              <div *ngIf="field.validation" class="flex items-center space-x-2">
                <div *ngIf="field.isValidating" class="flex items-center space-x-2 text-blue-600">
                  <svg class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-sm">Validating...</span>
                </div>
                
                <div *ngIf="!field.isValidating && field.validation.valid" class="flex items-center space-x-2 text-green-600">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-sm font-medium">Valid</span>
                </div>
                
                <div *ngIf="!field.isValidating && !field.validation.valid" class="flex items-center space-x-2 text-red-600">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-sm font-medium">Invalid</span>
                </div>
              </div>
            </div>

            <!-- Input Field -->
            <div class="space-y-3">
              <div class="relative">
                <input 
                  [type]="field.showKey ? 'text' : 'password'"
                  [(ngModel)]="field.value" 
                  [name]="field.service + 'Key'"
                  [placeholder]="field.placeholder"
                  (blur)="validateKey(field)"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-20"
                  [class.border-red-300]="field.validation && !field.validation.valid"
                  [class.border-green-300]="field.validation && field.validation.valid">
                
                <!-- Show/Hide Toggle -->
                <button type="button" 
                        (click)="toggleKeyVisibility(field)"
                        class="absolute right-2 top-2 p-1 text-gray-400 hover:text-gray-600">
                  <svg *ngIf="!field.showKey" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                  </svg>
                  <svg *ngIf="field.showKey" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"/>
                    <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"/>
                  </svg>
                </button>
              </div>

              <!-- Validation Error -->
              <div *ngIf="field.validation && !field.validation.valid && field.validation.error" 
                   class="text-sm text-red-600">
                {{ field.validation.error }}
              </div>

              <!-- Help Text -->
              <div class="flex items-center justify-between text-sm text-gray-500">
                <span>
                  Get your API key from 
                  <a [href]="field.helpUrl" target="_blank" rel="noopener noreferrer" 
                     class="text-blue-600 hover:text-blue-800 underline">
                    {{ getServiceName(field.service) }}
                  </a>
                </span>
                <button type="button" 
                        *ngIf="field.value"
                        (click)="clearKey(field)"
                        class="text-red-600 hover:text-red-800">
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
          <div class="flex space-x-3">
            <button type="button" 
                    (click)="validateAllKeys()" 
                    [disabled]="isValidating || !hasAnyKeys()"
                    class="btn-secondary">
              <svg *ngIf="!isValidating" class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
              <svg *ngIf="isValidating" class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isValidating ? 'Validating...' : 'Validate All Keys' }}
            </button>
            
            <button type="button" 
                    (click)="resetForm()"
                    class="btn-secondary">
              Reset
            </button>
          </div>
          
          <div class="flex space-x-3">
            <button type="button" 
                    (click)="onCancel()"
                    class="btn-secondary">
              Cancel
            </button>
            <button type="submit" 
                    [disabled]="isSaving || !hasChanges()"
                    class="btn-primary">
              <svg *ngIf="!isSaving" class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6a1 1 0 10-2 0v5.586l-1.293-1.293z"/>
                <path d="M5 3a2 2 0 00-2 2v1a1 1 0 002 0V5h10v10h-4a1 1 0 100 2h4a2 2 0 002-2V5a2 2 0 00-2-2H5z"/>
              </svg>
              <svg *ngIf="isSaving" class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSaving ? 'Saving...' : 'Save API Keys' }}
            </button>
          </div>
        </div>
      </form>

      <!-- Success/Error Messages -->
      <div *ngIf="message" 
           class="mt-4 p-4 rounded-lg"
           [ngClass]="{
             'bg-green-50 border border-green-200 text-green-800': message.type === 'success',
             'bg-red-50 border border-red-200 text-red-800': message.type === 'error'
           }">
        <div class="flex items-center space-x-2">
          <svg *ngIf="message.type === 'success'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
          <svg *ngIf="message.type === 'error'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
          </svg>
          <span class="text-sm font-medium">{{ message.text }}</span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .btn-primary {
      @apply px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center;
    }
    
    .btn-secondary {
      @apply px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center;
    }
  `]
})
export class ApiKeySettingsComponent implements OnInit, OnDestroy {
  private apiKeyService = inject(ApiKeyService);
  private subscriptions: Subscription[] = [];

  keyFields: KeyField[] = [
    {
      service: 'groq',
      label: 'Groq API Key',
      description: 'Used for AI text generation and chat functionality. Required for generating descriptions.',
      placeholder: 'Enter your Groq API key (gsk_...)',
      required: true,
      helpUrl: 'https://console.groq.com/keys',
      value: '',
      validation: null,
      isValidating: false,
      showKey: false
    },
    {
      service: 'deepgram',
      label: 'Deepgram API Key',
      description: 'Used for audio transcription. Required for processing audio files.',
      placeholder: 'Enter your Deepgram API key',
      required: true,
      helpUrl: 'https://console.deepgram.com/',
      value: '',
      validation: null,
      isValidating: false,
      showKey: false
    },
    {
      service: 'serpapi',
      label: 'SerpAPI Key',
      description: 'Used for web search and link validation. Optional but recommended for better results.',
      placeholder: 'Enter your SerpAPI key (optional)',
      required: false,
      helpUrl: 'https://serpapi.com/manage-api-key',
      value: '',
      validation: null,
      isValidating: false,
      showKey: false
    }
  ];

  keyStatus = { hasRequired: false, missing: [], optional: [] };
  isValidating = false;
  isSaving = false;
  message: { type: 'success' | 'error'; text: string } | null = null;
  private originalKeys: UserAPIKeys = {};

  ngOnInit() {
    this.loadApiKeys();
    this.updateKeyStatus();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadApiKeys() {
    this.subscriptions.push(
      this.apiKeyService.getUserApiKeys().subscribe(keys => {
        if (keys) {
          this.originalKeys = { ...keys };
          this.keyFields.forEach(field => {
            field.value = keys[field.service] || '';
          });
        }
        this.updateKeyStatus();
      })
    );
  }

  private updateKeyStatus() {
    this.keyStatus = this.apiKeyService.getKeyStatus();
  }

  async validateKey(field: KeyField) {
    if (!field.value.trim()) {
      field.validation = null;
      return;
    }

    field.isValidating = true;
    try {
      field.validation = await this.apiKeyService.validateApiKey(field.service, field.value);
    } catch (error) {
      field.validation = { valid: false, error: 'Validation failed', service: field.service };
    } finally {
      field.isValidating = false;
    }
  }

  async validateAllKeys() {
    this.isValidating = true;
    this.clearMessage();

    try {
      for (const field of this.keyFields) {
        if (field.value.trim()) {
          await this.validateKey(field);
        }
      }
      this.showMessage('success', 'All API keys validated successfully');
    } catch (error) {
      this.showMessage('error', 'Failed to validate some API keys');
    } finally {
      this.isValidating = false;
    }
  }

  async saveKeys() {
    this.isSaving = true;
    this.clearMessage();

    try {
      const keys: UserAPIKeys = {};
      this.keyFields.forEach(field => {
        if (field.value.trim()) {
          keys[field.service] = field.value.trim();
        }
      });

      await this.apiKeyService.saveApiKeys(keys);
      this.originalKeys = { ...keys };
      this.showMessage('success', 'API keys saved successfully');
      this.updateKeyStatus();
    } catch (error) {
      this.showMessage('error', 'Failed to save API keys');
    } finally {
      this.isSaving = false;
    }
  }

  toggleKeyVisibility(field: KeyField) {
    field.showKey = !field.showKey;
  }

  clearKey(field: KeyField) {
    field.value = '';
    field.validation = null;
  }

  resetForm() {
    this.keyFields.forEach(field => {
      field.value = this.originalKeys[field.service] || '';
      field.validation = null;
      field.showKey = false;
    });
    this.clearMessage();
  }

  hasAnyKeys(): boolean {
    return this.keyFields.some(field => field.value.trim().length > 0);
  }

  hasChanges(): boolean {
    return this.keyFields.some(field => {
      const originalValue = this.originalKeys[field.service] || '';
      return field.value.trim() !== originalValue;
    });
  }

  getServiceName(service: string): string {
    switch (service) {
      case 'groq': return 'Groq Console';
      case 'deepgram': return 'Deepgram Console';
      case 'serpapi': return 'SerpAPI';
      default: return service;
    }
  }

  onCancel() {
    this.resetForm();
  }

  private showMessage(type: 'success' | 'error', text: string) {
    this.message = { type, text };
    setTimeout(() => this.clearMessage(), 5000);
  }

  private clearMessage() {
    this.message = null;
  }
}
