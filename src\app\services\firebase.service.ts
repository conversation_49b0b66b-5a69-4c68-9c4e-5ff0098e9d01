import { Injectable, inject } from '@angular/core';
import { 
  Auth, 
  signInWithPopup, 
  GoogleAuthProvider, 
  signOut, 
  user,
  User as FirebaseUser
} from '@angular/fire/auth';
import { 
  Firestore, 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  addDoc,
  query,
  where,
  orderBy,
  limit,
  getDocs
} from '@angular/fire/firestore';
import { 
  Storage, 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from '@angular/fire/storage';
import { Observable, from, map, switchMap, of } from 'rxjs';
import { User, ProcessingSession, UserAPIKeys, UserPreferences } from '../models/podcast-description.model';

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  private auth = inject(Auth);
  private firestore = inject(Firestore);
  private storage = inject(Storage);

  // Auth state observable
  user$ = user(this.auth);

  constructor() {}

  // Authentication methods
  signInWithGoogle(): Observable<FirebaseUser | null> {
    const provider = new GoogleAuthProvider();
    return from(signInWithPopup(this.auth, provider)).pipe(
      switchMap((result) => {
        // Create or update user document
        if (result.user) {
          return from(this.createOrUpdateUser(result.user)).pipe(
            map(() => result.user)
          );
        }
        return of(result.user);
      }),
      map(user => user || null)
    );
  }

  async signOut(): Promise<void> {
    try {
      await signOut(this.auth);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  // User management
  private async createOrUpdateUser(firebaseUser: FirebaseUser): Promise<void> {
    const userRef = doc(this.firestore, 'users', firebaseUser.uid);
    const userDoc = await getDoc(userRef);

    const userData: Partial<User> = {
      uid: firebaseUser.uid,
      email: firebaseUser.email || '',
      displayName: firebaseUser.displayName || '',
      photoURL: firebaseUser.photoURL || '',
      lastLoginAt: new Date()
    };

    if (!userDoc.exists()) {
      // Create new user
      const newUser: User = {
        ...userData as User,
        apiKeys: {},
        preferences: {
          theme: 'auto',
          autoMode: false,
          notifications: {
            email: true,
            browser: true
          }
        },
        createdAt: new Date()
      };
      await setDoc(userRef, newUser);
    } else {
      // Update existing user
      await updateDoc(userRef, userData);
    }
  }

  // Get user data
  getUserData(uid: string): Observable<User | null> {
    const userRef = doc(this.firestore, 'users', uid);
    return from(getDoc(userRef)).pipe(
      map(doc => doc.exists() ? doc.data() as User : null)
    );
  }

  // Update user API keys
  async updateUserAPIKeys(uid: string, apiKeys: UserAPIKeys): Promise<void> {
    const userRef = doc(this.firestore, 'users', uid);
    await updateDoc(userRef, { apiKeys });
  }

  // Update user preferences
  async updateUserPreferences(uid: string, preferences: UserPreferences): Promise<void> {
    const userRef = doc(this.firestore, 'users', uid);
    await updateDoc(userRef, { preferences });
  }

  // Session management
  async createSession(session: Omit<ProcessingSession, 'id'>): Promise<string> {
    const sessionsRef = collection(this.firestore, 'sessions');
    const docRef = await addDoc(sessionsRef, session);
    return docRef.id;
  }

  async updateSession(sessionId: string, updates: Partial<ProcessingSession>): Promise<void> {
    const sessionRef = doc(this.firestore, 'sessions', sessionId);
    await updateDoc(sessionRef, { ...updates, updatedAt: new Date() });
  }

  getSession(sessionId: string): Observable<ProcessingSession | null> {
    const sessionRef = doc(this.firestore, 'sessions', sessionId);
    return from(getDoc(sessionRef)).pipe(
      map(doc => doc.exists() ? { id: doc.id, ...doc.data() } as ProcessingSession : null)
    );
  }

  getUserSessions(uid: string, limitCount: number = 10): Observable<ProcessingSession[]> {
    const sessionsRef = collection(this.firestore, 'sessions');
    const q = query(
      sessionsRef,
      where('userId', '==', uid),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );
    
    return from(getDocs(q)).pipe(
      map(snapshot => 
        snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as ProcessingSession))
      )
    );
  }

  // File storage
  async uploadAudioFile(file: File, sessionId: string, uid: string): Promise<string> {
    const fileName = `${sessionId}_${file.name}`;
    const filePath = `audio-files/${uid}/${sessionId}/${fileName}`;
    const fileRef = ref(this.storage, filePath);

    await uploadBytes(fileRef, file);
    return await getDownloadURL(fileRef);
  }

  async uploadTranscriptFile(file: File, sessionId: string, uid: string): Promise<string> {
    const fileName = `${sessionId}_transcript_${file.name}`;
    const filePath = `transcripts/${uid}/${sessionId}/${fileName}`;
    const fileRef = ref(this.storage, filePath);

    await uploadBytes(fileRef, file);
    return await getDownloadURL(fileRef);
  }

  async deleteAudioFile(filePath: string): Promise<void> {
    const fileRef = ref(this.storage, filePath);
    await deleteObject(fileRef);
  }

  // Utility methods
  getCurrentUser(): FirebaseUser | null {
    return this.auth.currentUser;
  }

  isAuthenticated(): boolean {
    return !!this.auth.currentUser;
  }
}
