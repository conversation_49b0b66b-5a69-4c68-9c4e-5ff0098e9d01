import { NextRequest } from 'next/server';
import { 
  handleC<PERSON>, 
  createApiResponse, 
  createErrorResponse, 
  verifyAuth, 
  verifyOwnership,
  validateRequestBody,
  getFirebaseAdmin 
} from './lib/utils/auth';
import { MemoryManager } from './lib/utils/memory-manager';
import { CoordinatorAgent } from './lib/agents/coordinator';
import { ProcessingRequest, ValidationError } from './lib/types';

export default async function handler(req: NextRequest) {
  // Handle CORS preflight
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  const origin = req.headers.get('origin');

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createErrorResponse('Method not allowed', 405, origin);
    }

    // Verify authentication
    const authContext = await verifyAuth(req);

    // Validate request body
    const { sessionId, audioFileUrl, userId, autoMode = false } = await validateRequestBody<ProcessingRequest>(
      req,
      ['sessionId', 'audioFileUrl', 'userId']
    );

    // Verify user owns this session
    verifyOwnership(authContext, userId);

    // Get Firebase admin instances
    const { firestore } = getFirebaseAdmin();

    // Verify session exists and user owns it
    const sessionDoc = await firestore
      .collection('sessions')
      .doc(sessionId)
      .get();

    if (!sessionDoc.exists) {
      throw new ValidationError('Session not found');
    }

    const sessionData = sessionDoc.data();
    if (!sessionData) {
      throw new ValidationError('Session data not found');
    }

    // Verify ownership again from session data
    verifyOwnership(authContext, sessionData.userId);

    // Initialize or get agent context
    let agentContext = MemoryManager.getContext(sessionId);
    if (!agentContext) {
      agentContext = MemoryManager.createInitialContext(sessionId, userId);
    }

    // Store audio file URL in memory
    MemoryManager.addMemory(sessionId, 'audioFileUrl', audioFileUrl);

    // Update session status in Firestore
    await firestore
      .collection('sessions')
      .doc(sessionId)
      .update({
        status: 'processing',
        updatedAt: new Date(),
      });

    // Start the coordinator
    const coordinator = CoordinatorAgent.getInstance();
    const result = await coordinator.orchestrateWorkflow(sessionId, undefined, autoMode);

    // Update session with initial result
    const updateData: any = {
      status: result.success ? 'processing' : 'error',
      updatedAt: new Date(),
    };

    if (result.updatedDescription) {
      updateData.description = result.updatedDescription;
    }

    await firestore
      .collection('sessions')
      .doc(sessionId)
      .update(updateData);

    // Prepare response
    const responseData = {
      sessionId,
      status: result.success ? 'processing' : 'error',
      message: result.message,
      nextAgent: result.nextAgent,
      shouldAskUser: result.shouldAskUser,
      agentContext: {
        sessionId: agentContext.sessionId,
        userId: agentContext.userId,
        memoriesCount: Object.keys(agentContext.memories).length,
        agentStatusesCount: Object.keys(agentContext.agentStatuses).length,
      },
      workflowStatus: coordinator.getWorkflowStatus(sessionId),
    };

    if (result.updatedDescription) {
      responseData.updatedDescription = result.updatedDescription;
    }

    return createApiResponse(
      {
        success: result.success,
        data: responseData,
        message: result.message,
      },
      200,
      origin
    );

  } catch (error) {
    console.error('Process audio error:', error);

    if (error instanceof ValidationError) {
      return createErrorResponse(error.message, error.statusCode, origin);
    }

    return createErrorResponse(
      error instanceof Error ? error.message : 'Processing failed',
      500,
      origin
    );
  }
}
